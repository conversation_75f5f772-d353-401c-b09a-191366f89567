-- CreateEnum
CREATE TYPE "ExtraIngredients" AS ENUM ('PEP<PERSON><PERSON><PERSON><PERSON>', 'MUSHROOMS', 'ONIONS', 'SAUSAGE', 'BACON', 'EXTRA_CHEESE', 'GREEN_PEPPERS', '<PERSON><PERSON><PERSON><PERSON>_OLIVES', 'PINEAPPLE', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'HAM', 'BEEF');

-- CreateTable
CREATE TABLE "Extra" (
    "id" TEXT NOT NULL,
    "name" "ExtraIngredients" NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "productId" TEXT NOT NULL,

    CONSTRAINT "Extra_pkey" PRIMARY KEY ("id")
);

-- AddForeignK<PERSON>
ALTER TABLE "Extra" ADD CONSTRAINT "Extra_productId_fkey" FOREIGN KEY ("productId") REFERENCES "Product"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
